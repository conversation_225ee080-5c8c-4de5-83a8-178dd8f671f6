"use client";

import * as React from "react";
import { useRouter } from "next/navigation";
import Link from "next/link";
import { <PERSON>Left, ChefHat } from "lucide-react";
import { toast } from "sonner";
import { useForm } from "react-hook-form";
import { zod<PERSON><PERSON>olver } from "@hookform/resolvers/zod";
import { Button } from "@/components/ui/button";
import { Typography } from "@/components/ui/typography";
import { LoadingState } from "@/components/ui/loading-state";
import {
  MultiStepForm,
  useMultiStepForm,
} from "@/components/ui/multi-step-form";
import { useUser, useIsProvider } from "@/hooks/use-auth";
import {
  useCreateProvider,
  useProviderStatus,
  type ProviderOnboardingData,
} from "@/hooks/use-provider-onboarding";
import { providerOnboardingSchema } from "@/lib/validations";
import type { ProviderOnboardingFormData } from "@/types/form.types";
import { usePerformanceMonitor } from "@/lib/utils/performance-monitor";

// Lazy load step components for better bundle splitting
const BusinessInfoStep = React.lazy(() =>
  import("@/components/onboarding/business-info-step").then((module) => ({
    default: module.BusinessInfoStep,
  }))
);
const ServiceDetailsStep = React.lazy(() =>
  import("@/components/onboarding/service-details-step").then((module) => ({
    default: module.ServiceDetailsStep,
  }))
);
const ContactInfoStep = React.lazy(() =>
  import("@/components/onboarding/contact-info-step").then((module) => ({
    default: module.ContactInfoStep,
  }))
);

// Constants for better maintainability
const STEPS = [
  {
    id: "business-info",
    title: "Business Information",
    description: "Tell us about your catering business",
  },
  {
    id: "service-details",
    title: "Service Details",
    description: "Describe your services and coverage area",
  },
  {
    id: "contact-info",
    title: "Contact Information",
    description: "How customers can reach you",
  },
];

const FORM_STORAGE_KEY = "onboarding-form-data";
const FORM_TIMESTAMP_KEY = "onboarding-form-timestamp";
const FORM_DATA_EXPIRY = 24 * 60 * 60 * 1000; // 24 hours

// Custom hook for debounced form persistence with performance tracking
function useFormPersistence<T>(
  formData: T,
  isDirty: boolean,
  trackPersistence?: (fn: () => void) => void,
  delay: number = 1000
) {
  const timeoutRef = React.useRef<NodeJS.Timeout | null>(null);

  React.useEffect(() => {
    if (!isDirty) return;

    // Clear existing timeout
    if (timeoutRef.current) {
      clearTimeout(timeoutRef.current);
    }

    // Set new timeout for debounced save
    timeoutRef.current = setTimeout(() => {
      const persistFn = () => {
        try {
          localStorage.setItem(FORM_STORAGE_KEY, JSON.stringify(formData));
          localStorage.setItem(FORM_TIMESTAMP_KEY, Date.now().toString());
        } catch (error) {
          console.warn("Failed to save form data:", error);
        }
      };

      if (trackPersistence) {
        trackPersistence(persistFn);
      } else {
        persistFn();
      }
    }, delay);

    return () => {
      if (timeoutRef.current) {
        clearTimeout(timeoutRef.current);
      }
    };
  }, [formData, isDirty, trackPersistence, delay]);
}

// Custom hook for form recovery
function useFormRecovery() {
  const [recoveryData, setRecoveryData] = React.useState<{
    data: ProviderOnboardingFormData | null;
    showPrompt: boolean;
  }>({ data: null, showPrompt: false });

  React.useEffect(() => {
    try {
      const savedData = localStorage.getItem(FORM_STORAGE_KEY);
      const savedTimestamp = localStorage.getItem(FORM_TIMESTAMP_KEY);

      if (savedData && savedTimestamp) {
        const isRecent =
          Date.now() - parseInt(savedTimestamp) < FORM_DATA_EXPIRY;

        if (isRecent) {
          const parsedData = JSON.parse(savedData);
          setRecoveryData({ data: parsedData, showPrompt: true });
        } else {
          // Clean up expired data
          localStorage.removeItem(FORM_STORAGE_KEY);
          localStorage.removeItem(FORM_TIMESTAMP_KEY);
        }
      }
    } catch (error) {
      console.warn("Failed to load saved form data:", error);
      localStorage.removeItem(FORM_STORAGE_KEY);
      localStorage.removeItem(FORM_TIMESTAMP_KEY);
    }
  }, []);

  const recoverData = React.useCallback(() => {
    setRecoveryData((prev) => ({ ...prev, showPrompt: false }));
    return recoveryData.data;
  }, [recoveryData.data]);

  const discardData = React.useCallback(() => {
    localStorage.removeItem(FORM_STORAGE_KEY);
    localStorage.removeItem(FORM_TIMESTAMP_KEY);
    setRecoveryData({ data: null, showPrompt: false });
  }, []);

  return {
    recoveryData: recoveryData.data,
    showRecoveryPrompt: recoveryData.showPrompt,
    recoverData,
    discardData,
  };
}

export default function ProviderOnboardingFlowPage() {
  const router = useRouter();
  const { data: user, isLoading: isUserLoading } = useUser();
  const { value: isProvider } = useIsProvider();

  // Performance monitoring
  const { trackValidation, trackPersistence, logSummary, sendToAnalytics } =
    usePerformanceMonitor("ProviderOnboardingFlow");

  // TanStack Query hooks
  const createProviderMutation = useCreateProvider();
  const { data: isExistingProvider, isLoading: isCheckingProvider } =
    useProviderStatus();

  // Form recovery hook
  const { showRecoveryPrompt, recoverData, discardData } = useFormRecovery();

  // Multi-step form state
  const { currentStep, nextStep, previousStep, canGoPrevious } =
    useMultiStepForm({
      totalSteps: STEPS.length,
    });

  // React Hook Form setup with optimized validation
  const form = useForm<ProviderOnboardingFormData>({
    resolver: zodResolver(providerOnboardingSchema),
    defaultValues: {
      businessName: "",
      businessAddress: "",
      logo: undefined,
      description: "",
      serviceAreas: [],
      sampleMenu: undefined,
      contactPersonName: "",
      mobileNumber: "",
      socialMediaLinks: {
        facebook: "",
        instagram: "",
        website: "",
      },
    },
    mode: "onChange",
  });

  const {
    formState: { errors, isDirty },
    getValues,
  } = form;

  // Optimized field watching - only watch specific fields needed for validation
  const businessName = form.watch("businessName");
  const description = form.watch("description");
  const serviceAreas = form.watch("serviceAreas");
  const contactPersonName = form.watch("contactPersonName");
  const mobileNumber = form.watch("mobileNumber");

  // State for step validation from dedicated components
  const [stepValidationState, setStepValidationState] = React.useState<
    Record<number, boolean>
  >({});

  // Optimized step validation with better memoization and performance tracking
  const stepValidation = React.useMemo(() => {
    return trackValidation(() => {
      const businessNameValid = businessName?.trim().length >= 2;
      const descriptionValid = description?.trim().length >= 10;
      const serviceAreasValid = serviceAreas?.length > 0;
      const contactNameValid = contactPersonName?.trim().length >= 2;
      const mobileValid = mobileNumber?.trim().length > 0;

      // Combine form validation with step component validation
      const formValidation: Record<number, boolean> = {
        1: businessNameValid && !errors.businessName,
        2:
          descriptionValid &&
          serviceAreasValid &&
          !errors.description &&
          !errors.serviceAreas,
        3:
          contactNameValid &&
          mobileValid &&
          !errors.contactPersonName &&
          !errors.mobileNumber,
      };

      // Use step validation state if available, otherwise fall back to form validation
      const result: Record<number, boolean> = {
        1: stepValidationState[1] ?? formValidation[1],
        2: stepValidationState[2] ?? formValidation[2],
        3: stepValidationState[3] ?? formValidation[3],
      };

      return result;
    });
  }, [
    trackValidation,
    businessName,
    description,
    serviceAreas,
    contactPersonName,
    mobileNumber,
    errors.businessName,
    errors.description,
    errors.serviceAreas,
    errors.contactPersonName,
    errors.mobileNumber,
    stepValidationState,
  ]);

  // Debounced form persistence with performance tracking
  useFormPersistence(getValues(), isDirty, trackPersistence); 

  // Recovery functions with proper memoization
  const handleRecoverData = React.useCallback(() => {
    const data = recoverData();
    if (data) {
      form.reset(data);
      toast.success("Form data recovered successfully!");
    }
  }, [recoverData, form]);

  const handleDiscardData = React.useCallback(() => {
    discardData();
  }, [discardData]);

  // Memoized data change handlers to prevent unnecessary re-renders
  const handleDataChange = React.useCallback(
    (data: Partial<ProviderOnboardingFormData>) => {
      Object.entries(data).forEach(([key, value]) => {
        form.setValue(key as keyof ProviderOnboardingFormData, value, {
          shouldValidate: true,
        });
      });
    },
    [form]
  );

  const handleValidationChange = React.useCallback(
    (step: number, isValid: boolean) => {
      setStepValidationState((prev) => ({ ...prev, [step]: isValid }));
    },
    []
  );

  // Optimized step content rendering with lazy loading and memoization
  const renderStepContent = React.useCallback(() => {
    const formData = form.getValues();

    switch (currentStep) {
      case 1:
        return (
          <React.Suspense fallback={<LoadingState variant="card" />}>
            <BusinessInfoStep
              data={{
                businessName: formData.businessName,
                businessAddress: formData.businessAddress,
                logo: formData.logo,
              }}
              onDataChange={handleDataChange}
              onValidationChange={(isValid) =>
                handleValidationChange(1, isValid)
              }
              form={form as any} // Type casting needed for compatibility
            />
          </React.Suspense>
        );
      case 2:
        return (
          <React.Suspense fallback={<LoadingState variant="card" />}>
            <ServiceDetailsStep
              data={{
                description: formData.description,
                serviceAreas: formData.serviceAreas,
                sampleMenu: formData.sampleMenu,
              }}
              onDataChange={handleDataChange}
              onValidationChange={(isValid) =>
                handleValidationChange(2, isValid)
              }
              form={form as any} // Type casting needed for compatibility
            />
          </React.Suspense>
        );
      case 3:
        return (
          <React.Suspense fallback={<LoadingState variant="card" />}>
            <ContactInfoStep
              data={{
                contactPersonName: formData.contactPersonName,
                mobileNumber: formData.mobileNumber,
                socialMediaLinks: formData.socialMediaLinks,
              }}
              onDataChange={handleDataChange}
              onValidationChange={(isValid) =>
                handleValidationChange(3, isValid)
              }
              form={form as any} // Type casting needed for compatibility
            />
          </React.Suspense>
        );
      default:
        return null;
    }
  }, [currentStep, form, handleDataChange, handleValidationChange]);

  // Memoized handlers to prevent unnecessary re-renders - MUST be before any conditional returns
  const handleNext = React.useCallback(() => {
    if (stepValidation[currentStep]) {
      nextStep();
    } else {
      // More specific error messages based on current step
      const stepNames = [
        "Business Information",
        "Service Details",
        "Contact Information",
      ];
      toast.error(
        `Please complete all required fields in ${
          stepNames[currentStep - 1]
        } before continuing.`
      );
    }
  }, [stepValidation, currentStep, nextStep]);

  // Enhanced form submission with react-hook-form validation
  const handleSubmit = React.useCallback(async () => {
    if (!stepValidation[currentStep]) {
      toast.error("Please complete all required fields.");
      return;
    }

    if (isExistingProvider) {
      toast.error("You are already a catering provider.");
      router.push("/dashboard");
      return;
    }

    // Validate all steps before submission
    const allStepsValid = Object.values(stepValidation).every(Boolean);
    if (!allStepsValid) {
      const invalidSteps = Object.entries(stepValidation)
        .filter(([, isValid]) => !isValid)
        .map(([step]) => {
          const stepNames = [
            "Business Information",
            "Service Details",
            "Contact Information",
          ];
          return stepNames[parseInt(step) - 1];
        });
      toast.error(`Please complete: ${invalidSteps.join(", ")}`);
      return;
    }

    // Get form data and validate
    const formData = form.getValues();
    const validationResult = providerOnboardingSchema.safeParse(formData);

    if (!validationResult.success) {
      toast.error("Please fix the validation errors before submitting.");
      return;
    }

    // Validate service areas
    if (!formData.serviceAreas || formData.serviceAreas.length === 0) {
      toast.error("Please provide at least one service area.");
      return;
    }

    const submissionData: ProviderOnboardingData = {
      businessName: formData.businessName.trim(),
      businessAddress: formData.businessAddress?.trim(),
      logo: formData.logo,
      description: formData.description.trim(),
      serviceAreas: formData.serviceAreas,
      sampleMenu: formData.sampleMenu,
      contactPersonName: formData.contactPersonName.trim(),
      mobileNumber: formData.mobileNumber.trim(),
      socialMediaLinks: formData.socialMediaLinks,
    };

    createProviderMutation.mutate(submissionData, {
      onSuccess: () => {
        // Clear saved form data on successful submission
        localStorage.removeItem("onboarding-form-data");
        localStorage.removeItem("onboarding-form-timestamp");

        // Log performance metrics and send to analytics
        logSummary();
        sendToAnalytics(user?.id);

        toast.success(
          "🎉 Onboarding completed successfully! Welcome to CateringHub!"
        );
        router.push("/dashboard");
      },
      onError: (error) => {
        console.error("Error submitting onboarding:", error);

        // More specific error messages
        const errorMessage =
          error instanceof Error ? error.message : "Unknown error occurred";
        if (errorMessage.includes("duplicate")) {
          toast.error(
            "You already have a provider profile. Redirecting to dashboard..."
          );
          setTimeout(() => router.push("/dashboard"), 2000);
        } else if (
          errorMessage.includes("network") ||
          errorMessage.includes("fetch")
        ) {
          toast.error(
            "Network error. Please check your connection and try again."
          );
        } else {
          toast.error(
            "Submission failed. Please check your information and try again."
          );
        }
      },
    });
  }, [
    stepValidation,
    currentStep,
    isExistingProvider,
    form,
    createProviderMutation,
    router,
  ]);

  // Simple redirect logic
  React.useEffect(() => {
    if (isUserLoading || isCheckingProvider) return;

    if (!user) {
      router.push(
        "/login?redirect=" + encodeURIComponent("/onboarding/provider/flow")
      );
      return;
    }

    if (isProvider || isExistingProvider) {
      router.push("/dashboard");
      return;
    }
  }, [
    user,
    isUserLoading,
    isProvider,
    isExistingProvider,
    isCheckingProvider,
    router,
  ]);

  // Show loading state with skeleton loaders
  if (isUserLoading || isCheckingProvider) {
    return (
      <div className="min-h-screen bg-background">
        {/* Header Skeleton */}
        <header className="border-b border-border">
          <div className="container mx-auto px-4 py-4 flex items-center justify-between">
            <div className="flex items-center gap-2">
              <div className="h-6 w-6 bg-muted rounded animate-pulse"></div>
              <div className="h-6 w-32 bg-muted rounded animate-pulse"></div>
            </div>
            <div className="h-9 w-20 bg-muted rounded animate-pulse"></div>
          </div>
        </header>

        {/* Main Content Skeleton */}
        <main className="container mx-auto px-4 py-8">
          <div className="w-full max-w-4xl mx-auto">
            {/* Progress Steps Skeleton */}
            <div className="mb-8">
              <div className="flex items-center justify-between">
                {[1, 2, 3].map((step) => (
                  <div
                    key={step}
                    className="flex flex-col items-center space-y-2"
                  >
                    <div className="w-8 h-8 bg-muted rounded-full animate-pulse"></div>
                    <div className="h-4 w-20 bg-muted rounded animate-pulse"></div>
                  </div>
                ))}
              </div>
            </div>

            {/* Form Card Skeleton */}
            <LoadingState variant="card" count={1} showFooter={true} />
          </div>
        </main>
      </div>
    );
  }

  // Don't render if user is not logged in (will redirect)
  if (!user) {
    return null;
  }

  return (
    <div className="min-h-screen bg-background">
      {/* Recovery Prompt with improved accessibility */}
      {showRecoveryPrompt && (
        <div
          className="fixed inset-0 bg-black/50 flex items-center justify-center z-50"
          role="dialog"
          aria-modal="true"
          aria-labelledby="recovery-title"
          aria-describedby="recovery-description"
        >
          <div className="bg-white rounded-lg p-6 max-w-md mx-4 shadow-xl">
            <h3 id="recovery-title" className="text-lg font-semibold mb-2">
              Recover Previous Data?
            </h3>
            <p id="recovery-description" className="text-gray-600 mb-4">
              We found some previously saved form data. Would you like to
              recover it?
            </p>
            <div className="flex gap-2 justify-end">
              <Button
                variant="outline"
                onClick={handleDiscardData}
                aria-label="Discard saved data and start fresh"
              >
                Start Fresh
              </Button>
              <Button
                onClick={handleRecoverData}
                aria-label="Recover previously saved form data"
              >
                Recover Data
              </Button>
            </div>
          </div>
        </div>
      )}

      {/* Header */}
      <header className="border-b border-border">
        <div className="container mx-auto px-4 py-4 flex items-center justify-between">
          <div className="flex items-center gap-2">
            <ChefHat className="h-6 w-6" />
            <Typography variant="h5">CateringHub</Typography>
          </div>

          <Button variant="ghost" asChild>
            <Link
              href="/onboarding/provider"
              className="flex items-center gap-2"
            >
              <ArrowLeft className="h-4 w-4" />
              Back
            </Link>
          </Button>
        </div>
      </header>

      {/* Main Content */}
      <main className="container mx-auto px-4 py-8">
        <MultiStepForm
          steps={STEPS}
          currentStep={currentStep}
          onNext={handleNext}
          onPrevious={previousStep}
          onSubmit={handleSubmit}
          canGoNext={stepValidation[currentStep] || false}
          canGoPrevious={canGoPrevious}
          isSubmitting={createProviderMutation.isPending}
          title="Provider Onboarding"
          description="Complete your catering provider profile to start accepting bookings"
          showProgress={true}
          progressOrientation="horizontal"
        >
          {renderStepContent()}
        </MultiStepForm>
      </main>
    </div>
  );
}
